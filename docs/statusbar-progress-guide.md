# VSCode 状态栏进度显示指南

## 概述

本指南介绍如何在 VSCode 的状态栏（底部栏）中显示进度信息。我们已经在设置同步功能中实现了状态栏进度显示，可以在导入设置时实时显示进度。

## 功能特性

- 在状态栏左侧显示进度信息
- 支持旋转加载图标
- 可以动态更新进度文本
- 自动清理状态栏条目

## 实现方式

### 1. 导入必要的服务

```typescript
import { IStatusbarService, StatusbarAlignment, IStatusbarEntryAccessor } from '../../../services/statusbar/browser/statusbar.js';
```

### 2. 注入状态栏服务

```typescript
async run(accessor: ServicesAccessor): Promise<void> {
    const statusbarService = accessor.get(IStatusbarService);
    // ... 其他代码
}
```

### 3. 创建状态栏更新函数

```typescript
let statusbarEntry: IStatusbarEntryAccessor | undefined;
const updateStatusbar = (message: string, showProgress: boolean = true) => {
    const statusEntry = {
        name: localize('importProgress', "Import Progress"),
        text: showProgress ? `$(loading~spin) ${message}` : message,
        ariaLabel: message,
        tooltip: message
    };
    
    if (statusbarEntry) {
        statusbarEntry.update(statusEntry);
    } else {
        statusbarEntry = statusbarService.addEntry(
            statusEntry,
            'import.progress',
            StatusbarAlignment.LEFT,
            -Number.MAX_VALUE // 显示在最左边
        );
    }
};
```

### 4. 使用状态栏显示进度

```typescript
// 初始显示
updateStatusbar('正在初始化...');

// 更新进度
updateStatusbar('正在处理数据...');

// 完成时显示（不带旋转图标）
updateStatusbar('完成！', false);

// 清理状态栏
if (statusbarEntry) {
    statusbarEntry.dispose();
    statusbarEntry = undefined;
}
```

## 测试功能

我们提供了一个测试命令来演示状态栏进度功能：

1. 打开命令面板（Cmd+Shift+P 或 Ctrl+Shift+P）
2. 输入 "Test Statusbar Progress"
3. 执行命令，观察状态栏左侧的进度显示

## 在设置导入中的应用

在设置导入功能中，状态栏会显示以下进度：

1. "Importing [产品名] settings..." - 导入设置时
2. "Importing disabled extensions state..." - 导入禁用扩展状态时  
3. "Importing extensions..." - 导入扩展时
4. 扩展导入过程中的具体进度信息
5. 完成后自动清理状态栏显示

## 最佳实践

1. **及时清理**: 操作完成后务必调用 `dispose()` 清理状态栏条目
2. **有意义的文本**: 使用清晰、简洁的进度描述文本
3. **适当的图标**: 使用 `$(loading~spin)` 表示进行中的操作
4. **无障碍支持**: 设置合适的 `ariaLabel` 和 `tooltip`
5. **唯一标识**: 为每个状态栏条目使用唯一的 ID

## 图标说明

- `$(loading~spin)`: 旋转的加载图标
- `$(check)`: 完成图标
- `$(error)`: 错误图标
- `$(warning)`: 警告图标

更多图标可以参考 VSCode 的图标库。
