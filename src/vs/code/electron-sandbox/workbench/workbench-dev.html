<!-- Copyright (C) Microsoft Corporation. All rights reserved. -->
<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<meta
			http-equiv="Content-Security-Policy"
			content="
				default-src
					'none'
				;
				img-src
					'self'
					data:
					blob:
					vscode-remote-resource:
					vscode-managed-remote-resource:
					https:
					https://cdnfile.corp.kuaishou.com
					https://h2.static.yximgs.com
					https://h1.static.yximgs.com
					https://ali.a.yximgs.com
					http://p.qpic.cn
					http://p.qlogo.cn
				;
				media-src
					'self'
					https://cdnfile.corp.kuaishou.com
					https://h2.static.yximgs.com
					https://h1.static.yximgs.com
					https://ali.a.yximgs.com
				;
				frame-src
					'self'
					vscode-webview:
				;
				script-src
					'self'
					'unsafe-eval'
					blob:
					'nonce-0c6a828f1297'
				;
				style-src
					'self'
					'unsafe-inline'
				;
				connect-src
					'self'
					https:
					ws:
				;
				font-src
					'self'
					data:
					vscode-remote-resource:
					vscode-managed-remote-resource:
					https://*.vscode-unpkg.net
				;
				require-trusted-types-for
					'script'
				;
				trusted-types
					vscode-bootstrapImportMap
					amdLoader
					cellRendererEditorText
					defaultWorkerFactory
					diffEditorWidget
					diffReview
					domLineBreaksComputer
					dompurify
					editorGhostText
					editorViewLayer
					notebookRenderer
					stickyScrollViewLayer
					tokenizeToString
					notebookChatEditController
					kwaipilot
					iconify
					lexical
				;
		"/>
	</head>

	<body aria-label="">
	</body>

	<!-- Startup (do not modify order of script tags!) -->
	<script src="../../../../bootstrap-window.js"></script>
	<script src="./workbench.js"></script>
</html>
