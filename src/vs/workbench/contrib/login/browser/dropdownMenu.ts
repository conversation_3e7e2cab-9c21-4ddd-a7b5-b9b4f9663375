import { addDisposableListener, EventType, getActiveWindow, clearNode } from '../../../../base/browser/dom.js';
import { Disposable, IDisposable } from '../../../../base/common/lifecycle.js';
import { onDidChangeFullscreen } from '../../../../base/browser/browser.js';
import './media/dropdownMenu.css';

export interface IDropdownMenuOptions {
	triggerElement: HTMLElement;
	container: HTMLElement;
	menuItems: IDropdownMenuItem[];
	onSelect?: (item: IDropdownMenuItem) => void;
	placement?: 'left-start';
}

export type IDropdownMenuItem = {
	id: string;
	label: string | HTMLElement;
	icon?: string;
	disabled?: boolean;
	readonly?: boolean;
	command?: string;
	children?: Array<IDropdownMenuItem>;
} | {
	separator: boolean;
};

export class DropdownMenu extends Disposable {
	private menuElement!: HTMLElement;
	private visible: boolean = false;
	private disposables: IDisposable[] = [];
	// 存储菜单项ID与DOM元素的映射关系
	private menuItemMap: Map<string, HTMLElement> = new Map();
	private subMenus: Map<string, DropdownMenu> = new Map();

	constructor(private options: IDropdownMenuOptions) {
		super();

		this.create();
		this.registerListeners();
	}

	private create(): void {
		// Create menu container
		this.menuElement = document.createElement('div');
		this.menuElement.className = 't-dropdown-menu';
		this.options.container.appendChild(this.menuElement);

		// Create menu items (支持级联)
		this.createMenuItems(this.options.menuItems, this.menuElement);
	}

	private createMenuItems(menuItems: IDropdownMenuItem[], parentElement: HTMLElement): void {
		menuItems.forEach(item => {
			if ('separator' in item) {
				const divider = document.createElement('div');
				divider.className = 't-dropdown-menu-divider';
				parentElement.appendChild(divider);
			} else {
				const menuItem = document.createElement('div');
				menuItem.className = 't-dropdown-menu-item';
				if (item.disabled) {
					menuItem.classList.add('disabled');
				}
				if (item.readonly) {
					menuItem.classList.add('readonly');
				}
				if (item.icon) {
					const icon = document.createElement('span');
					icon.className = `icon ${item.icon}`;
					menuItem.appendChild(icon);
				}

				const text = document.createElement('span');
				text.className = 'text';
				if (typeof item.label === 'string') {
					text.textContent = item.label;
				} else {
					text.appendChild(item.label);
				}
				menuItem.appendChild(text);

				if (!item.disabled) {
					menuItem.addEventListener('click', () => {
						this.options.onSelect?.(item);
						if (!item.children) {
							this.hide();
						}
					});
					menuItem.addEventListener('mouseenter', () => {
						for (const key of this.subMenus.keys()) {
							if (key === item.id) {
								this.subMenus.get(key)?.show();
							} else {
								this.subMenus.get(key)?.hide();
							}
						}
					});
				}

				parentElement.appendChild(menuItem);
				if ('id' in item) {
					this.menuItemMap.set(item.id, menuItem);
				}

				// 级联菜单支持
				if (item.children && item.children.length > 0) {
					// 安全地创建SVG元素
					const svgElement = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
					svgElement.setAttribute('width', '16');
					svgElement.setAttribute('height', '16');
					svgElement.setAttribute('viewBox', '0 0 16 16');
					svgElement.setAttribute('fill', 'none');
					const pathElement = document.createElementNS('http://www.w3.org/2000/svg', 'path');
					pathElement.setAttribute('fill-rule', 'evenodd');
					pathElement.setAttribute('clip-rule', 'evenodd');
					pathElement.setAttribute('d', 'M10.072 8.024L5.715 3.667L6.333 3.047L11 7.716V8.334L6.333 13L5.715 12.381L10.072 8.024Z');
					pathElement.setAttribute('fill', 'currentColor');
					svgElement.appendChild(pathElement);
					menuItem.appendChild(svgElement);

					if (this.subMenus.has(item.id)) {
						return;
					}
					const submenus = new DropdownMenu({
						triggerElement: menuItem,
						menuItems: item.children,
						container: this.options.container,
						placement: 'left-start',
						onSelect: this.options.onSelect
					});

					this.subMenus.set(item.id, submenus);
				}
			}
		});
	}



	/**
	 * 根据ID查找菜单项DOM元素
	 */
	private findMenuItemById(id: string): HTMLElement | undefined {
		return this.menuItemMap.get(id);
	}

	/**
	 * 动态更新指定菜单项的内容
	 */
	public updateMenuItem(id: string, newLabel: string | HTMLElement): void {
		const menuItem = this.findMenuItemById(id);
		if (!menuItem) {
			return;
		}

		const textElement = menuItem.querySelector('.text');
		if (textElement) {
			// 使用安全的方式清空现有内容，避免TrustedHTML错误
			clearNode(textElement as HTMLElement);

			if (typeof newLabel === 'string') {
				textElement.textContent = newLabel;
			} else {
				textElement.appendChild(newLabel);
			}
		}
	}

	/**
	 * 动态设置指定菜单项的禁用状态
	 */
	public setMenuItemDisabled(id: string, disabled: boolean): void {
		const menuItem = this.findMenuItemById(id);
		if (!menuItem) {
			return;
		}

		if (disabled) {
			menuItem.classList.add('disabled');
			// 移除点击事件监听器
			const newMenuItem = menuItem.cloneNode(true) as HTMLElement;
			menuItem.parentNode?.replaceChild(newMenuItem, menuItem);
			// 更新映射
			this.menuItemMap.set(id, newMenuItem);
		} else {
			menuItem.classList.remove('disabled');
			// 重新添加点击事件监听器
			const originalItem = this.options.menuItems.find(item => 'id' in item && item.id === id);
			if (originalItem && 'id' in originalItem) {
				menuItem.addEventListener('click', () => {
					this.options.onSelect?.(originalItem);
					this.hide();
				});
			}
		}
	}

	private registerListeners(): void {
		const targetWindow = getActiveWindow();
		// Toggle menu on trigger click
		this._register(addDisposableListener(this.options.triggerElement, EventType.CLICK, (e) => {
			e.preventDefault();
			e.stopPropagation();
			this.toggle();
		}));

		// Hide menu when clicking outside
		this._register(addDisposableListener(targetWindow.document, EventType.CLICK, (e) => {
			if (!this.menuElement.contains(e.target as Node) &&
				!this.options.triggerElement.contains(e.target as Node)) {
				this.hide();
			}
		}));

		// Hide menu when pressing escape
		this._register(addDisposableListener(targetWindow.document, EventType.KEY_DOWN, (e) => {
			if (e.key === 'Escape') {
				this.hide();
			}
		}));

		// Update position when fullscreen mode changes
		this._register(onDidChangeFullscreen(windowId => {
			if (windowId === (targetWindow as any).vscodeWindowId && this.visible) {
				// Use setTimeout to ensure the fullscreen transition is complete
				setTimeout(() => {
					this.updatePosition();
				}, 100);
			}
		}));

		// Update position when window is resized
		this._register(addDisposableListener(targetWindow, EventType.RESIZE, () => {
			if (this.visible) {
				this.updatePosition();
			}
		}));
	}

	private updatePosition(): void {
		const targetWindow = getActiveWindow();
		const triggerRect = this.options.triggerElement.getBoundingClientRect();
		const menuRect = this.menuElement.getBoundingClientRect();
		const viewportHeight = targetWindow.innerHeight;

		// Position menu below trigger by default
		let top = triggerRect.bottom;
		let left = triggerRect.left;

		if (this.options.placement === 'left-start') {
			left = left - menuRect.width - 13;
			top = triggerRect.top;
		} else {
			// If menu would go below viewport, position it above trigger instead
			if (top + menuRect.height > viewportHeight) {
				top = triggerRect.top - menuRect.height;
			}

			// If menu would go off the right edge of the viewport, adjust its left position
			if (left + menuRect.width > targetWindow.innerWidth) {
				left = targetWindow.innerWidth - menuRect.width - 10;
			}
		}

		this.menuElement.style.top = `${top}px`;
		this.menuElement.style.left = `${left}px`;
	}

	public show(): void {
		if (!this.visible) {
			this.menuElement.classList.add('show');
			this.options.triggerElement.classList.add('active');
			this.updatePosition();
			this.visible = true;
		}
	}

	public hide(): void {
		if (this.visible) {
			this.menuElement.classList.remove('show');
			this.visible = false;
			this.options.triggerElement.classList.remove('active');
		}
	}

	public toggle(): void {
		this.visible ? this.hide() : this.show();
	}

	override dispose(): void {
		super.dispose();
		this.menuElement.remove();
		this.disposables.forEach(d => d.dispose());
		this.disposables = [];
	}
}
