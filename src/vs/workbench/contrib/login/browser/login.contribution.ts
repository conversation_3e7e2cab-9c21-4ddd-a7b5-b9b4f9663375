/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

// VSCode会自动加载相应目录下的CSS文件

import { localize } from '../../../../nls.js';
import { registerAction2, Action2, MenuId, MenuRegistry } from '../../../../platform/actions/common/actions.js';
import { ServicesAccessor, createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { IContextKeyService } from '../../../../platform/contextkey/common/contextkey.js';
import { Codicon } from '../../../../base/common/codicons.js';
import { INotificationService } from '../../../../platform/notification/common/notification.js';
import { Disposable, DisposableStore } from '../../../../base/common/lifecycle.js';
import { ILogService } from '../../../../platform/log/common/log.js';
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { IStorageService, StorageScope, StorageTarget } from '../../../../platform/storage/common/storage.js';
import { IOpenerService } from '../../../../platform/opener/common/opener.js';
import { getDeviceId, getSSOUrl, getUserTokenInfo, TaskRunner } from './loginUtils.js';
import { getActiveWindow } from '../../../../base/browser/dom.js';
import { getRadarInstance } from '../../../browser/radar.js';
import './media/login.css';
import { DropdownMenu, type IDropdownMenuItem } from './dropdownMenu.js';
import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { DEVICE_ID_KEY, LOGIN_STATUS_KEY, USER_INFO_KEY, UserInfo } from '../../../../base/common/kwaipilot/login.js';
import { IProductService } from '../../../../platform/product/common/productService.js';
import { Registry } from '../../../../platform/registry/common/platform.js';
import { Extensions as WorkbenchExtensions, IWorkbenchContributionsRegistry, IWorkbenchContribution } from '../../../common/contributions.js';
import { LifecyclePhase } from '../../../services/lifecycle/common/lifecycle.js';
import { IUpdateService, StateType } from '../../../../platform/update/common/update.js';
import { IEditCodeService } from '../../kwaipilot/common/editCode.js';
import { IDialogService } from '../../../../platform/dialogs/common/dialogs.js';

// 登录服务接口
export const ILoginService = createDecorator<ILoginService>('loginService');

export interface ILoginService {
	readonly _serviceBrand: undefined;
	readonly isLoggedIn: boolean;
	readonly currentUser: UserInfo | undefined;
	login(): Promise<void>;
	logout(): Promise<void>;
	checkStatus(): Promise<void>;
}

// 登录服务实现
class LoginService extends Disposable implements ILoginService {
	declare readonly _serviceBrand: undefined;

	private _isLoggedIn: boolean = false;
	private _currentUser: UserInfo | undefined;
	private _userNameElement: HTMLElement | undefined;

	constructor(
		@INotificationService private readonly notificationService: INotificationService,
		@IContextKeyService private readonly contextKeyService: IContextKeyService,
		@ILogService private readonly logService: ILogService,
		@IStorageService private readonly storageService: IStorageService,
		@IOpenerService private readonly openerService: IOpenerService,
		@IProductService private readonly productService: IProductService,
		@ICommandService private readonly commandService: ICommandService,
		@IEditCodeService private readonly editCodeService: IEditCodeService,
		@IDialogService private readonly dialogService: IDialogService,
	) {
		super();
		this.checkStatus();
	}

	private updateUserName(): void {
		if (this._userNameElement) {
			if (this._currentUser) {
				this._userNameElement.style.display = '';
				this._userNameElement.textContent = this._currentUser.displayName || this._currentUser.name;
			} else {
				this._userNameElement.style.display = 'none';
			}
		}
	}

	get isLoggedIn(): boolean {
		return this._isLoggedIn;
	}

	get currentUser(): UserInfo | undefined {
		return this._currentUser;
	}

	/**
	 * 同步用户信息到插件
	 * 通过 command 将用户信息变更通知到插件系统
	 */
	private syncUserInfoToPlugin(userInfo: UserInfo | undefined) {
		try {
			// 添加调试日志
			this.logService.info('Attempting to sync user info to plugin', {
				userInfo: userInfo ? userInfo.name : 'undefined',
				hasCommandService: !!this.commandService
			});

			// 调用插件的用户信息同步command
			this.commandService.executeCommand('kwaipilot.bridge.userInfo', userInfo);
			this.logService.info('User info synced to plugin successfully', userInfo?.name || 'logout');
		} catch (error) {
			// command调用失败不应影响登录流程，只记录错误
			this.logService.error('Failed to sync user info to plugin:', error);
		}
	}

	async login(): Promise<void> {
		try {
			const deviceId = getDeviceId(this.storageService);
			const ssoUrl = getSSOUrl(deviceId);
			if (!this.storageService.get(DEVICE_ID_KEY, StorageScope.APPLICATION)) {
				await this.openerService.open((ssoUrl));
			} else {
				try {
					const userInfo = await getUserTokenInfo(deviceId, this.logService);
					this._currentUser = userInfo;
				} catch (error) {
					await this.openerService.open((ssoUrl));
				}
			}

			const onResolve = async (result: UserInfo | undefined) => {
				// 添加调试日志，检查this上下文
				this.logService.info('onResolve callback called', {
					hasResult: !!result,
					thisType: typeof this,
					hasSyncMethod: typeof this.syncUserInfoToPlugin
				});

				if (result) {
					this.storageService.store(
						DEVICE_ID_KEY,
						this.storageService.get(DEVICE_ID_KEY, StorageScope.APPLICATION) || deviceId,
						StorageScope.APPLICATION,
						StorageTarget.MACHINE
					);
					this._isLoggedIn = true;
					this._currentUser = result;
					this.contextKeyService.createKey(LOGIN_STATUS_KEY, true);

					// 存储用户信息到扩展的全局状态中
					this.storageService.store(
						USER_INFO_KEY,
						JSON.stringify({
							name: result.name,
							ticket: result.ticket || '',
							mail: result.mail || '',
							displayName: result.displayName || result.name,
							avatar: result.avatar || '',
						}),
						StorageScope.APPLICATION,
						StorageTarget.MACHINE
					);
					this.notificationService.info(localize('loginSuccess', "Login successful!"));
					this.logService.info('User logged in successfully');
					this.updateUserName();

					// 同步用户信息到插件
					this.syncUserInfoToPlugin(result);
				}
			};

			const onReject = (error: any, context: { times: number; stop: () => void }) => {
				this.logService.error(`Login check failed: ${error}, times: ${context.times}`);
			};

			const checkLoginStatusTaskRunner = new TaskRunner<UserInfo | undefined>({
				task: () => getUserTokenInfo(deviceId, this.logService),
				onResolve,
				onReject,
				isFirstSuccessStop: true,
				isFirstFailStop: false,
				times: 5,
				timeGap: 2000,
			});
			checkLoginStatusTaskRunner.run();
		} catch (error) {
			this.logService.error('Failed to login:', error);
			this.notificationService.error(localize('loginFailed', "Login failed: {0}", error.message));
		} finally {
			this.updateUserName();
		}
	}

	/** logoutConfirmed 前的逻辑  */
	async logout(): Promise<void> {
		const hasUnhandledDiffsLength = this.editCodeService.hasUnhandledDiffs();
		if (hasUnhandledDiffsLength) {
			const result = await this.dialogService.prompt({
				type: 'question',
				// allow-any-unicode-next-line
				message: localize('unhandledChangesTitle', "存在未处理代码变更"),
				// allow-any-unicode-next-line
				detail: localize('unhandledChangesDetail', "{0}个文件存在未处理的代码变更，请返回决策后再新建对话", hasUnhandledDiffsLength),
				buttons: [
					{
						// allow-any-unicode-next-line
						label: localize('acceptAll', "全部接受"),
						run: () => 'accept'
					},
					{
						// allow-any-unicode-next-line
						label: localize('rejectAll', "全部拒绝"),
						run: () => 'reject'
					},
					{
						// allow-any-unicode-next-line
						label: localize('cancel', "取消"),
						run: () => 'cancel'
					}
				]
			});

			if (result.result === 'accept') {
				// 用户点击了"全部接受"
				await this.editCodeService.acceptAllDiffs();
				await this.logoutConfirmed();
			} else if (result.result === 'reject') {
				// 用户点击了"全部拒绝"
				await this.editCodeService.rejectAllDiffs();
				await this.logoutConfirmed();
			}
			// 如果用户点击了"取消"或关闭对话框，则不执行任何操作
		} else {
			await this.logoutConfirmed();
		}
	}

	async logoutConfirmed(): Promise<void> {
		try {
			this.storageService.remove(USER_INFO_KEY, StorageScope.APPLICATION);
			this.storageService.remove(DEVICE_ID_KEY, StorageScope.APPLICATION);
			this._isLoggedIn = false;
			this._currentUser = undefined;
			this.contextKeyService.createKey(LOGIN_STATUS_KEY, false);
			this.notificationService.info(localize('logoutSuccess', "Logout successful!"));
			this.logService.info('User logged out successfully');
			this.updateUserName();

			// 同步登出状态到插件
			this.syncUserInfoToPlugin(undefined);
		} catch (error) {
			this.logService.error('Failed to logout:', error);
			this.notificationService.error(localize('logoutFailed', "Logout failed: {0}", error.message));
		}
	}

	async checkStatus(): Promise<void> {
		try {
			const deviceId = getDeviceId(this.storageService);
			const userInfoStr = this.storageService.get(USER_INFO_KEY, StorageScope.APPLICATION);
			let userInfo: UserInfo | undefined;

			if (userInfoStr) {
				try {
					userInfo = JSON.parse(userInfoStr);
				} catch (e) {
					this.logService.error('Failed to parse user info:', e);
				}
			}

			if (!userInfo) {
				try {
					userInfo = await getUserTokenInfo(deviceId, this.logService);
					if (userInfo) {
						this.storageService.store(USER_INFO_KEY, JSON.stringify(userInfo), StorageScope.APPLICATION, StorageTarget.MACHINE);
					}
				} catch (e) {
					this.logService.error('Failed to get user info from server:', e);
				}
			}
			this._isLoggedIn = !!userInfo;
			this._currentUser = userInfo;
			this.contextKeyService.createKey(LOGIN_STATUS_KEY, this._isLoggedIn);
			this.logService.info(`Login status checked: ${this._isLoggedIn ? 'logged in' : 'not logged in'}`);
			this.updateUserName();

			getRadarInstance()?.setDimensions({
				userId: userInfo?.name,
				deviceId: deviceId,
				versionName: this.productService.appVersion,
			});
			getRadarInstance()?.event({
				name: 'CHECK_LOGIN_STATUS', // 必填
				result_type: this._isLoggedIn ? 'success' : 'failed',
				extra_info: {
					userId: userInfo?.name,
					deviceId: deviceId,
					versionName: this.productService.appVersion,
				}
			});

			// 同步状态检查结果到插件
			this.syncUserInfoToPlugin(userInfo);
		} catch (error) {
			this.logService.error('Failed to check login status:', error);
			this._isLoggedIn = false;
			this._currentUser = undefined;
			this.contextKeyService.createKey(LOGIN_STATUS_KEY, false);
			this.updateUserName();

			// 同步错误状态到插件
			this.syncUserInfoToPlugin(undefined);
		}
	}

	override dispose(): void {
		super.dispose();
		this._userNameElement?.remove();
	}
}

// 注册登录服务
registerSingleton(ILoginService, LoginService, InstantiationType.Eager);

// 添加登录服务初始化贡献类
class LoginServiceInitializer extends Disposable implements IWorkbenchContribution {
	static readonly ID = 'workbench.contrib.loginServiceInitializer';

	constructor(
		@ILoginService private readonly loginService: ILoginService,
		@ILogService private readonly logService: ILogService
	) {
		super();
		this.loginService.checkStatus();
		// 服务已经通过依赖注入被实例化，这里可以添加额外的初始化逻辑
		this.logService.info('LoginService initialized during workbench startup');
	}
}

// 注册工作台贡献，确保在启动早期阶段就创建 LoginService
Registry.as<IWorkbenchContributionsRegistry>(WorkbenchExtensions.Workbench).registerWorkbenchContribution(
	LoginServiceInitializer,
	LifecyclePhase.Restored // 在视图恢复阶段初始化
);

// 新增登录Popover命令ID
const LOGIN_POPOVER_COMMAND_ID = 'workbench.action.loginPopover';

/**
 * 根据更新服务状态获取对应的标签文本
 */
function getUpdateLabel(state: any): string {
	let updateLabel = 'Check for Updates...';

	switch (state.type) {
		case StateType.CheckingForUpdates:
			updateLabel = 'Checking for Updates...';
			break;
		case StateType.AvailableForDownload:
			updateLabel = 'Download Update';
			break;
		case StateType.Downloading:
			updateLabel = 'Downloading Update...';
			break;
		case StateType.Downloaded:
			updateLabel = 'Install Update';
			break;
		case StateType.Ready:
			updateLabel = 'Restart to Update';
			break;
		case StateType.Idle:
			if (state.error) {
				updateLabel = 'Update Failed';
			}
			break;
	}

	return updateLabel;
}

/**
 * 判断更新操作是否应该被禁用
 */
function isUpdateActionDisabled(state: any): boolean {
	return state.type === StateType.CheckingForUpdates || state.type === StateType.Downloading;
}

// 注册设置命令
registerAction2(class OpenSettingsAction extends Action2 {
	constructor() {
		super({
			id: 'myExtension.openSettings',
			title: { value: localize('openSettings', "Open Settings"), original: 'Open Settings' },
			icon: Codicon.settings,
			f1: false
		});
	}

	async run(accessor: ServicesAccessor): Promise<void> {
		const commandService = accessor.get(ICommandService);
		// 打开VSCode设置页面
		await commandService.executeCommand('workbench.action.openSettings2');
	}
});

// 注册登录Popover命令，点击icon时弹出popover菜单
registerAction2(class LoginPopoverAction extends Action2 {
	private readonly disposables = new DisposableStore();
	constructor() {
		super({
			id: LOGIN_POPOVER_COMMAND_ID,
			title: { value: localize('loginPopover', "Login"), original: 'Login' },
			icon: Codicon.account,
			f1: false
		});
	}

	async run(accessor: ServicesAccessor): Promise<void> {
		const loginService = accessor.get(ILoginService);
		const commandService = accessor.get(ICommandService);
		const storageService = accessor.get(IStorageService);
		const updateService = accessor.get(IUpdateService);
		const user = loginService.currentUser;
		// 获取icon DOM节点作为锚点
		const targetWindow = getActiveWindow();
		const iconEl = targetWindow.document.querySelector('.monaco-workbench .part.titlebar .titlebar-container .titlebar-right .action-toolbar-container .codicon-account') as HTMLElement | null;

		if (iconEl) {
			let menuItems: IDropdownMenuItem[] = [];


			// 获取更新状态，根据状态显示不同的菜单项文本
			const state = updateService.state;
			const updateLabel = getUpdateLabel(state);

			// 登录和非登录状态下共用的菜单；
			const commonItems = [
				{ id: 'settings', label: 'Kwaipilot Settings', command: 'kwaipilot.openBasicsManagement' },
				{ id: 'shortcuts', label: 'Keyboard Shortcuts', command: 'workbench.action.openGlobalKeybindings' },
				{ id: 'snippet', label: 'Snippets', command: 'workbench.action.openSnippets' },
				{ id: 'tasks', label: 'Tasks', command: 'workbench.action.tasks.openUserTasks' },
				{ id: 'theme', label: 'Themes', children: [{ id: 'fileIconTheme', label: 'File Icon Theme', command: 'workbench.action.selectIconTheme' }, { id: 'colorTheme', label: 'Color Theme', command: 'workbench.action.selectTheme' }, { id: 'productIconTheme', label: 'Product Icon Theme', command: 'workbench.action.selectProductIconTheme' }] },
				{ id: 'checkUpdate', label: updateLabel, disabled: isUpdateActionDisabled(state) }
			];

			if (user) {
				const profileEle = targetWindow.document.createElement('div');
				// 	const childHTML = `
				// <div class="layout-frame">
				// 	<img class="content-frame" src="${user.avatar}" style="width:24px;height:24px;" />
				// 	<span class="user-name">${user.displayName || user.name}</span>
				// </div>
				// `;
				const layout = targetWindow.document.createElement('div');
				layout.className = 'layout-frame';
				const img = targetWindow.document.createElement('img');
				img.className = 'avatar';
				img.style = 'width:24px;height:24px;';
				if (user.avatar) { img.src = user.avatar; }

				const name = targetWindow.document.createElement('span');
				name.className = 'user-name';
				name.textContent = user.displayName || user.name;

				layout.appendChild(img);
				layout.appendChild(name);
				profileEle.appendChild(layout);
				menuItems = [
					{ id: 'profile', label: profileEle, readonly: true },
					{ separator: true },
					...commonItems,
					{ separator: true },
					{ id: 'logout', label: 'Logout' }
				];
			} else {
				const loginButton = targetWindow.document.createElement('div');
				loginButton.className = 'btn';
				loginButton.textContent = 'Log in';
				menuItems = [
					{ id: 'login', label: loginButton },
					{ separator: true },
					...commonItems
				];
			}
			// 创建下拉菜单 fixed 固定位置布局
			const menu = new DropdownMenu({
				triggerElement: iconEl,
				container: targetWindow.document.querySelector('.monaco-workbench') || targetWindow.document.body,
				menuItems,
				onSelect: (item) => {
					if ('command' in item && !!item.command) {
						commandService.executeCommand(item.command);
					} else if ('id' in item) {
						switch (item.id) {
							case 'login': {
								getRadarInstance().event({
									name: 'app_login',
									event_type: 'business',
									extra_info: {
										entry: "side_dropdown_menu"
									}
								});
								loginService.login();
								menu.dispose();
								break;
							}
							case 'logout': {
								loginService.logout();
								menu.dispose();
								break;
							}
							case 'checkUpdate': {
								// 根据更新状态执行不同的命令
								const state = updateService.state;
								switch (state.type) {
									case StateType.AvailableForDownload:
										updateService.downloadUpdate();
										break;
									case StateType.Downloaded:
										updateService.applyUpdate();
										break;
									case StateType.Ready:
										updateService.quitAndInstall();
										break;
									default:
										updateService.checkForUpdates(true);
										break;
								}
								break;
							}
							default:
								break;
						}
					}
				}
			});

			// 监听更新服务状态变化，动态更新菜单项
			const updateStateListener = updateService.onStateChange((newState) => {
				const newUpdateLabel = getUpdateLabel(newState);
				const shouldDisable = isUpdateActionDisabled(newState);
				menu.updateMenuItem('checkUpdate', newUpdateLabel);
				menu.setMenuItemDisabled('checkUpdate', shouldDisable);
			});

			menu.show();

			this.disposables.add(menu);
			this.disposables.add(updateStateListener);
			this.disposables.add(
				storageService.onDidChangeValue(StorageScope.APPLICATION, USER_INFO_KEY, this.disposables)(() => {
					menu.dispose();
				})
			);
		}
	}
	dispose(): void {
		this.disposables.dispose();
	}
});

// 将登录icon添加到标题栏，点击后触发Popover命令
MenuRegistry.appendMenuItem(MenuId.TitleBar, {
	command: {
		id: LOGIN_POPOVER_COMMAND_ID,
		title: localize('loginPopover', "Login"),
		icon: Codicon.account
	},
	group: 'navigation',
	order: 10  // 设置较大的order值，确保在question图标右侧
});
