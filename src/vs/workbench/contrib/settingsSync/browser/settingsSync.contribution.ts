/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { localize } from '../../../../nls.js';
import { registerAction2, Action2 } from '../../../../platform/actions/common/actions.js';
import { IAction } from '../../../../base/common/actions.js';
import { ISettingsSyncService } from '../../../../platform/settingsSync/common/settingsSync.js';
import { ServicesAccessor } from '../../../../platform/instantiation/common/instantiation.js';
import { Categories } from '../../../../platform/action/common/actionCommonCategories.js';
import { INotificationService, Severity } from '../../../../platform/notification/common/notification.js';
import { Registry } from '../../../../platform/registry/common/platform.js';
import { Extensions as WorkbenchExtensions, IWorkbenchContributionsRegistry } from '../../../../workbench/common/contributions.js';
import { LifecyclePhase } from '../../../../workbench/services/lifecycle/common/lifecycle.js';
import { ISettingsSyncProgressService } from '../../../../platform/settingsSync/common/settingsSyncProgress.js';
import { SettingsSyncProgressService } from './settingsSyncProgressService.js';
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { SettingsSyncService } from '../../../../platform/settingsSync/common/settingsSyncService.js';
import { vscodeForkProductNameMap } from '../../../../platform/environment/common/environmentService.js';
import { IDialogService } from '../../../../platform/dialogs/common/dialogs.js';
import { showExtensionFailureDialog } from './extensionFailureDialog.js';
import { IExtensionsSyncService } from '../../../../platform/extensionsSync/browser/extensionsSyncService.js';
import { IStatusbarService, StatusbarAlignment, IStatusbarEntryAccessor } from '../../../services/statusbar/browser/statusbar.js';
// Register services
registerSingleton(ISettingsSyncProgressService, SettingsSyncProgressService, InstantiationType.Eager);
registerSingleton(ISettingsSyncService, SettingsSyncService, InstantiationType.Eager);

// Register as workbench contribution
Registry.as<IWorkbenchContributionsRegistry>(WorkbenchExtensions.Workbench)
	.registerWorkbenchContribution(SettingsSyncProgressService, LifecyclePhase.Restored);



// Base class: Common settings import Action
abstract class BaseImportSettingsAction extends Action2 {
	private targetProductName: string;
	constructor(
		private readonly productId: keyof typeof vscodeForkProductNameMap,
		order: number
	) {
		const targetProductName = vscodeForkProductNameMap[productId].name;
		super({
			id: `workbench.action.import${targetProductName}Settings`,
			title: {
				value: localize('importSettings', "Import {0} Settings", targetProductName),
				original: `Import ${targetProductName} Settings`
			},
			category: Categories.Preferences,
			f1: true
		});
		this.targetProductName = targetProductName;
	}
	private async showPreview(accessor: ServicesAccessor): Promise<boolean> {
		const dialogService = accessor.get(IDialogService);
		const result = await dialogService.confirm({
			message: localize('preview title', "Import settings preview"),
			detail: localize('preview detail', "This will overwrite your current IDE settings and cannot be undone. Do you want to continue?"
			),
			primaryButton: localize('import', "Import"),
			cancelButton: localize('cancel', "Cancel"),
			type: Severity.Info
		});

		return result.confirmed;
	}

	async run(accessor: ServicesAccessor, options?: { showSecondAlert?: boolean }): Promise<void> {
		const settingsSyncService = accessor.get(ISettingsSyncService);
		const extensionsSyncService = accessor.get(IExtensionsSyncService);
		const notificationService = accessor.get(INotificationService);
		const dialogService = accessor.get(IDialogService);
		const statusbarService = accessor.get(IStatusbarService);

		const importStatusMap = {
			setting: {
				success: false,
				msg: ''
			},
			extension: {
				success: false,
				msg: ''
			},
			disabledExtensions: {
				success: false,
				msg: ''
			}
		};

		// Show preview
		if (options?.showSecondAlert !== false) {
			const shouldImport = await this.showPreview(accessor);
			if (!shouldImport) {
				return;
			}
		}

		// 展示导入中的提示，然后在最下面导入完成的时候消失
		const importingNotification = notificationService.notify({
			severity: Severity.Info,
			message: localize('importing', "Importing {0} settings, extensions and disabled extensions state...", this.targetProductName),
			sticky: true // 保持显示直到手动关闭
		});

		// 在状态栏显示进度信息
		let statusbarEntry: IStatusbarEntryAccessor | undefined;
		const updateStatusbar = (message: string, showProgress: boolean = true) => {
			const statusEntry = {
				name: localize('importProgress', "Import Progress"),
				text: showProgress ? `$(loading~spin) ${message}` : message,
				ariaLabel: message,
				tooltip: message
			};

			if (statusbarEntry) {
				statusbarEntry.update(statusEntry);
			} else {
				statusbarEntry = statusbarService.addEntry(
					statusEntry,
					'import.progress',
					StatusbarAlignment.LEFT,
					-Number.MAX_VALUE // 显示在最左边
				);
			}
		};

		// 初始状态栏显示
		updateStatusbar(localize('importingSettings', "Importing {0} settings...", this.targetProductName));

		// Import settings
		try {
			const isOk = await settingsSyncService.importSettings(this.productId);
			const isMcpOk = await settingsSyncService.importMcpSettings(this.productId);
			importStatusMap.setting.success = isOk && isMcpOk;
		} catch (error) {
			importStatusMap.setting.msg = localize('settingsImportError', "Failed to import settings: {0}", error.message);
		}

		// 更新状态栏：导入禁用扩展状态
		updateStatusbar(localize('importingDisabledExtensions', "Importing disabled extensions state..."));

		// Import disabled extensions state
		try {
			const isDisabledExtensionsOk = await settingsSyncService.importDisabledExtensions(this.productId);
			importStatusMap.disabledExtensions.success = isDisabledExtensionsOk;
		} catch (error) {
			importStatusMap.disabledExtensions.msg = localize('disabledExtensionsImportError', "Failed to import disabled extensions: {0}", error.message);
		}

		// 更新状态栏：导入扩展
		updateStatusbar(localize('importingExtensions', "Importing extensions..."));

		const extensionResult = await extensionsSyncService.importExtensions(this.productId, (message) => {
			// 更新状态栏显示扩展导入进度
			updateStatusbar(message);
		});
		// Import extensions
		// let extensionResult: { success: boolean; failedExtensions: Array<IImportExtensionInfo> } | undefined;
		try {
			// // 临时模拟失败数据，用于测试 UI
			// extensionResult = {
			// 	success: false,
			// 	failedExtensions: [
			// 		{ id: 'ms-python.python', displayName: 'Python', iconUrl: '', version: '2025.4.1', tip: 'Failed to download extension: Network error', status: 'failed' },
			// 		{ id: 'vscodevim.vim', displayName: 'Vim', tip: 'Extension not found in marketplace', version: '1.0.0', iconUrl: 'https://open-vsx.org/vscode/asset/ms-toolsai/jupyter/2025.4.1/Microsoft.VisualStudio.Services.Icons.Default?targetPlatform=universal', status: 'skipped' },
			// 		{ id: 'dbaeumer.vscode-eslint', displayName: 'ESLint', tip: 'Failed to download extension: Network error', version: '1.0.0', iconUrl: 'https://open-vsx.org/vscode/asset/ms-toolsai/jupyter/2025.4.1/Microsoft.VisualStudio.Services.Icons.Default?targetPlatform=universal', status: 'failed' },
			// 		{ id: 'ms-vscode.cpptools', displayName: 'C/C++', tip: 'Version conflict: Required ^2.0.0 but found 1.9.0', version: '1.0.0', iconUrl: 'https://open-vsx.org/vscode/asset/ms-toolsai/jupyter/2025.4.1/Microsoft.VisualStudio.Services.Icons.Default?targetPlatform=universal', status: 'skipped' },
			// 		{ id: 'ms-python.python', displayName: 'Python', iconUrl: 'https://open-vsx.org/vscode/asset/ms-toolsai/jupyter/2025.4.1/Microsoft.VisualStudio.Services.Icons.Default?targetPlatform=universal', version: '2025.4.1', tip: 'Failed to download extension: Network error', status: 'failed' },
			// 		{ id: 'vscodevim.vim', displayName: 'Vim', tip: 'Extension not found in marketplace', version: '1.0.0', iconUrl: 'https://open-vsx.org/vscode/asset/ms-toolsai/jupyter/2025.4.1/Microsoft.VisualStudio.Services.Icons.Default?targetPlatform=universal', status: 'skipped' },
			// 		{ id: 'dbaeumer.vscode-eslint', displayName: 'ESLint', tip: 'Failed to download extension: Network error', version: '1.0.0', iconUrl: 'https://open-vsx.org/vscode/asset/ms-toolsai/jupyter/2025.4.1/Microsoft.VisualStudio.Services.Icons.Default?targetPlatform=universal', status: 'failed' },
			// 		{ id: 'ms-vscode.cpptools', displayName: 'C/C++', tip: 'Version conflict: Required ^2.0.0 but found 1.9.0', version: '1.0.0', iconUrl: 'https://open-vsx.org/vscode/asset/ms-toolsai/jupyter/2025.4.1/Microsoft.VisualStudio.Services.Icons.Default?targetPlatform=universal', status: 'skipped' },
			// 		{ id: 'ms-python.python', displayName: 'Python', iconUrl: 'https://open-vsx.org/vscode/asset/ms-toolsai/jupyter/2025.4.1/Microsoft.VisualStudio.Services.Icons.Default?targetPlatform=universal', version: '2025.4.1', tip: 'Failed to download extension: Network error', status: 'failed' },
			// 		{ id: 'vscodevim.vim', displayName: 'Vim', tip: 'Extension not found in marketplace', version: '1.0.0', iconUrl: 'https://open-vsx.org/vscode/asset/ms-toolsai/jupyter/2025.4.1/Microsoft.VisualStudio.Services.Icons.Default?targetPlatform=universal', status: 'skipped' },
			// 		{ id: 'dbaeumer.vscode-eslint', displayName: 'ESLint', tip: 'Failed to download extension: Network error', version: '1.0.0', iconUrl: 'https://open-vsx.org/vscode/asset/ms-toolsai/jupyter/2025.4.1/Microsoft.VisualStudio.Services.Icons.Default?targetPlatform=universal', status: 'failed' },
			// 		{ id: 'ms-vscode.cpptools', displayName: 'C/C++', tip: 'Version conflict: Required ^2.0.0 but found 1.9.0', version: '1.0.0', iconUrl: 'https://open-vsx.org/vscode/asset/ms-toolsai/jupyter/2025.4.1/Microsoft.VisualStudio.Services.Icons.Default?targetPlatform=universal', status: 'skipped' },
			// 	]
			// };
			importStatusMap.extension.success = extensionResult.success;
			if (extensionResult.failedExtensions.length > 0) {
				// When some extensions fail to import
				const failedCount = extensionResult.failedExtensions.length;
				const message = localize('extensionsImportPartiallyFailed',
					"Failed to import {1} extensions while importing {0} extensions",
					vscodeForkProductNameMap[this.productId].id,
					failedCount
				);

				const details = extensionResult.failedExtensions
					.map(ext => `${ext.id}: ${ext.tip}`)
					.join('\n');

				importStatusMap.extension.msg = `${message}\n${details}`;
			}
		} catch (error) {
			importStatusMap.extension.msg = localize('extensionsImportError', "Failed to import extensions: {0}", error.message);
		}

		// Show notification
		const title = localize('importComplete', "Import from {0} Complete.", this.targetProductName);

		// 构建描述信息，包含禁用插件状态导入结果
		let description = '';
		if (!importStatusMap.extension.success) {
			description = localize('partialSuccess', "Settings, Keybindings and MCP settings were imported successfully, {0} extensions failed to import",
				extensionResult?.failedExtensions.length || 0
			);
		}

		if (!importStatusMap.disabledExtensions.success) {
			const disabledExtensionsMsg = localize('disabledExtensionsPartialSuccess', "Disabled extensions state import failed");
			description = description ? `${description}. ${disabledExtensionsMsg}` : disabledExtensionsMsg;
		}

		// 准备通知按钮和操作
		const primaryButtons: IAction[] = [{
			id: 'ok',
			label: localize('ok', "OK"),
			tooltip: '',
			class: undefined,
			enabled: true,
			run: () => { }
		}];

		// 如果有扩展导入失败，添加查看详情按钮
		if (extensionResult?.failedExtensions && extensionResult.failedExtensions.length > 0) {
			primaryButtons.push({
				id: 'viewDetails',
				label: localize('viewDetails', "View Failed Details"),
				tooltip: localize('viewDetailsTooltip', "View details of failed extension imports"),
				class: undefined,
				enabled: true,
				run: () => {
					showExtensionFailureDialog(dialogService, extensionResult.failedExtensions);
				}
			});
		}

		// 关闭导入中的通知
		importingNotification.close();

		// 清理状态栏进度显示
		if (statusbarEntry) {
			statusbarEntry.dispose();
			statusbarEntry = undefined;
		}

		// 显示通知
		notificationService.notify({
			severity: Severity.Info,
			message: `
${title}
${description}`,
			actions: {
				primary: primaryButtons,
			}
		});
	}
}

// VS Code settings import
class ImportVSCodeSettingsAction extends BaseImportSettingsAction {
	constructor() {
		super('Code', 1);
	}
}

// Cursor settings import
class ImportCursorSettingsAction extends BaseImportSettingsAction {
	constructor() {
		super('Cursor', 2);
	}
}

// Test action to demonstrate statusbar progress
class TestStatusbarProgressAction extends Action2 {
	constructor() {
		super({
			id: 'workbench.action.testStatusbarProgress',
			title: {
				value: localize('testStatusbarProgress', "Test Statusbar Progress"),
				original: 'Test Statusbar Progress'
			},
			category: Categories.Developer,
			f1: true
		});
	}

	async run(accessor: ServicesAccessor): Promise<void> {
		const statusbarService = accessor.get(IStatusbarService);

		let statusbarEntry: IStatusbarEntryAccessor | undefined;
		const updateStatusbar = (message: string, showProgress: boolean = true) => {
			const statusEntry = {
				name: localize('testProgress', "Test Progress"),
				text: showProgress ? `$(loading~spin) ${message}` : message,
				ariaLabel: message,
				tooltip: message
			};

			if (statusbarEntry) {
				statusbarEntry.update(statusEntry);
			} else {
				statusbarEntry = statusbarService.addEntry(
					statusEntry,
					'test.progress',
					StatusbarAlignment.LEFT,
					-Number.MAX_VALUE
				);
			}
		};

		// 演示进度更新
		updateStatusbar('Step 1: Initializing...');
		await new Promise(resolve => setTimeout(resolve, 2000));

		updateStatusbar('Step 2: Processing...');
		await new Promise(resolve => setTimeout(resolve, 2000));

		updateStatusbar('Step 3: Finalizing...');
		await new Promise(resolve => setTimeout(resolve, 2000));

		updateStatusbar('Completed!', false);
		await new Promise(resolve => setTimeout(resolve, 1000));

		// 清理状态栏
		if (statusbarEntry) {
			statusbarEntry.dispose();
		}
	}
}

// Register all import Actions
registerAction2(ImportVSCodeSettingsAction);
registerAction2(ImportCursorSettingsAction);
registerAction2(TestStatusbarProgressAction);
